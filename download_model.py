#!/usr/bin/env python3
"""
BAGEL 模型下载脚本 - 智能网络检测版
"""

import os
import sys
from pathlib import Path

def check_network():
    """检测网络环境"""
    print("🌐 检测网络环境...")

    try:
        import requests
        # 检测 HuggingFace 访问性
        response = requests.get("https://huggingface.co", timeout=5)
        if response.status_code == 200:
            print("✅ HuggingFace 可直接访问")
            return True
    except:
        print("❌ HuggingFace 无法访问，请开启 VPN")
        return False

def download_model():
    """下载 BAGEL 模型"""
    try:
        from huggingface_hub import snapshot_download
    except ImportError:
        print("❌ 错误: huggingface_hub 未安装")
        return False

    # 使用 models 目录而不是 checkpoints
    save_dir = "./models/BAGEL-7B-MoT"
    repo_id = "ByteDance-Seed/BAGEL-7B-MoT"

    print(f"🤗 开始下载模型: {repo_id}")
    print(f"📁 保存位置: {save_dir}")
    print("📊 模型大小约 14GB，请耐心等待...")

    # 创建目录
    Path(save_dir).mkdir(parents=True, exist_ok=True)

    try:
        snapshot_download(
            repo_id=repo_id,
            local_dir=save_dir,
            local_dir_use_symlinks=False,
            resume_download=True,
            allow_patterns=["*.json", "*.safetensors", "*.bin", "*.py", "*.md", "*.txt"],
        )

        print("✅ 模型下载完成!")
        return True

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查网络连接")
        print("2. 确保 VPN 正常工作")
        print("3. 检查磁盘空间 (需要至少 20GB)")
        print("4. 重新运行此脚本 (支持断点续传)")
        return False

def main():
    """主函数"""
    print("🥯 BAGEL 模型下载器")
    print("=" * 40)

    # 检查网络
    if not check_network():
        print("\n💡 解决方案:")
        print("1. 开启 VPN 并重新运行")
        print("2. 检查网络连接")
        sys.exit(1)

    # 下载模型
    print("\n📥 开始下载...")
    success = download_model()

    if success:
        print("\n🎉 模型下载完成!")
        print("\n📝 下一步:")
        print("1. 运行测试: python quick_verify.py")
        print("2. 启动应用: python app.py")
    else:
        print("\n❌ 下载失败，请检查网络后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
